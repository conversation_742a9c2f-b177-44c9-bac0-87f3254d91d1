#include "KDataInclude.h"
#include "bmruntime_interface.h"
#include "aiEngine/KBitlandAIEngine.h"
#include "KCondition.h"
#include "algorithm/KAIEngine.h"
#include "KControllerAIEngineManager.h"
#include "module/ImageProcess/KImageProcess.h"

enum ImageProcessType
{
    ImageProcessType_KeepRatioResize,  // 图像固定比例缩放模块测试
    ImageProcessType_Resize,           // 图像缩放模块测试
    ImageProcessType_Filter,           // 图像滤波模块测试
    ImageProcessType_Enhance,          // 图像增强模块测试
    ImageProcessType_Binary,           // 图像二值化模块测试
    ImageProcessType_Morphology,       // 图像形态学处理模块测试
    ImageProcessType_ShadowCorrection, // 图像阴影校正模块测试
};

int main(int argc, char *argv[])
{
    system("mkdir -p saveimages");
    system("rm -rf saveimages/*");
    ImageProcessType imageProcessType = ImageProcessType_ShadowCorrection;

    cv::Mat img = cv::imread("test.png");
    if (img.empty())
    {
        std::cout << "Failed to load image" << std::endl;
        return -1;
    }
    std::cout << "img w = " << img.cols << " h = " << img.rows << std::endl;
    cv::cvtColor(img, img, cv::COLOR_BGR2RGB);
    KImage2Data image2In(img);

    auto start = std::chrono::high_resolution_clock::now();
    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> elapsed;
    switch (imageProcessType)
    {
    case ImageProcessType_KeepRatioResize:
    {
        KKeepRatioResizeModule mKeepRatioResizeModule;
        mKeepRatioResizeModule.setId("mKeepRatioResizeModule");
        KInt widthorheight, dstheight, dstwidth;
        widthorheight.setValue(1);
        dstheight.setValue(1024);
        dstwidth.setValue(1024);

        mKeepRatioResizeModule.getParamData("image2In")->bind(&image2In);

        mKeepRatioResizeModule.getParamData("widthorheight")->bind(&widthorheight);
        mKeepRatioResizeModule.getParamData("dstheight")->bind(&dstheight);
        mKeepRatioResizeModule.getParamData("dstwidth")->bind(&dstwidth);

        start = std::chrono::high_resolution_clock::now();
        mKeepRatioResizeModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "图像固定比例缩放模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;

        KImage2Data image2Out;
        image2Out.bind(mKeepRatioResizeModule.getResultData("image2Out"));
        cv::cvtColor(image2Out.Image2().src(), image2Out.Image2().src(), cv::COLOR_RGB2BGR);
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());
    }
    break;
    case ImageProcessType_Resize:
    {
        KImageResizeModule mImageResizeModule;
        mImageResizeModule.setId("mImageResizeModule");
        KInt interpolationType, dstheight, dstwidth;
        interpolationType.setValue(1);
        dstheight.setValue(1024);
        dstwidth.setValue(1024);

        mImageResizeModule.getParamData("image2In")->bind(&image2In);

        mImageResizeModule.getParamData("interpolationType")->bind(&interpolationType);
        mImageResizeModule.getParamData("dstheight")->bind(&dstheight);
        mImageResizeModule.getParamData("dstwidth")->bind(&dstwidth);

        start = std::chrono::high_resolution_clock::now();
        mImageResizeModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "图像缩放模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;

        KImage2Data image2Out;
        image2Out.bind(mImageResizeModule.getResultData("image2Out"));
        cv::cvtColor(image2Out.Image2().src(), image2Out.Image2().src(), cv::COLOR_RGB2BGR);
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());
    }
    break;
    case ImageProcessType_Filter:
    {
        KImageFilterModule mImageFilterModule;
        mImageFilterModule.setId("mImageFilterModule");
        KInt filterType;
        filterType.setValue(1);
        KInt gaussianKernel, medianKernel, meanWidthKernel, meanHeightKernel;
        gaussianKernel.setValue(3);
        medianKernel.setValue(3);
        meanWidthKernel.setValue(3);
        meanHeightKernel.setValue(3);

        mImageFilterModule.getParamData("image2In")->bind(&image2In);
        mImageFilterModule.getParamData("filterType")->bind(&filterType);
        mImageFilterModule.getParamData("gaussianKernel")->bind(&gaussianKernel);
        mImageFilterModule.getParamData("medianKernel")->bind(&medianKernel);
        mImageFilterModule.getParamData("meanWidthKernel")->bind(&meanWidthKernel);
        mImageFilterModule.getParamData("meanHeightKernel")->bind(&meanHeightKernel);

        start = std::chrono::high_resolution_clock::now();
        mImageFilterModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "图像滤波模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;

        KImage2Data image2Out;
        image2Out.bind(mImageFilterModule.getResultData("image2Out"));
        cv::cvtColor(image2Out.Image2().src(), image2Out.Image2().src(), cv::COLOR_RGB2BGR);
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());
    }
    break;
    case ImageProcessType_Enhance:
    {
        KInt kfilttertype;
        kfilttertype.setValue(1);

        KImageEnhancedModule mEnhanceModule;
        mEnhanceModule.setId("mEnhanceModule");

        mEnhanceModule.getParamData("image2In")->bind(&image2In);
        std::string model = "aimodels/Zero_DCE++_single_output.bmodel";

        KAIEngineInferEnity *e = KControllerAIEngineManager::singleInstance()->createEnity(model);
        ((KSmartParamAIData *)mEnhanceModule.getParamData("smartAi"))->smartAiData()->setEnity(e);
        mEnhanceModule.getParamData("filterType")->bind(&kfilttertype);

        start = std::chrono::high_resolution_clock::now();
        mEnhanceModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "图像增强模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;

        KImage2Data image2Out;
        image2Out.bind(mEnhanceModule.getResultData("image2Out"));
        cv::cvtColor(image2Out.Image2().src(), image2Out.Image2().src(), cv::COLOR_RGB2BGR);
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());
    }
    break;
    case ImageProcessType_Binary:
    {
        // 测试所有四种二值化方法
        int binaryType = 0;
        KImageBinaryModule mImageBinaryModule;
        mImageBinaryModule.setId("mImageBinaryModule");

        // 设置基本参数
        KInt binaryTypeParam, thresholdType;
        binaryTypeParam.setValue(binaryType);
        thresholdType.setValue(0); // 二进制

        // 设置阈值参数
        KInt lowThreshold, highThreshold;
        lowThreshold.setValue(70);
        highThreshold.setValue(255);

        // 设置均值二值化参数
        KInt meanWidthKernel, meanHeightKernel;
        meanWidthKernel.setValue(11);
        meanHeightKernel.setValue(11);

        // 设置高斯二值化参数
        KInt gaussianKernel;
        gaussianKernel.setValue(11);
        KDouble gaussianStandardDeviation;
        gaussianStandardDeviation.setValue(5.0);

        // 设置比较类型和阈值差值
        KInt compareType, thresholdDiff;
        compareType.setValue(0);
        thresholdDiff.setValue(10);

        // 设置Sauvola参数
        KInt sauvolaWidthKernel, sauvolaHeightKernel, sauvolaSegmentType;
        KDouble sauvolaValue, sauvolaRange;
        sauvolaWidthKernel.setValue(31);
        sauvolaHeightKernel.setValue(31);
        sauvolaValue.setValue(0.15);
        sauvolaRange.setValue(128.0);
        sauvolaSegmentType.setValue(0);

        // 绑定参数
        mImageBinaryModule.getParamData("image2In")->bind(&image2In);
        mImageBinaryModule.getParamData("binaryType")->bind(&binaryTypeParam);
        mImageBinaryModule.getParamData("lowThreshold")->bind(&lowThreshold);
        mImageBinaryModule.getParamData("highThreshold")->bind(&highThreshold);
        mImageBinaryModule.getParamData("thresholdType")->bind(&thresholdType);
        mImageBinaryModule.getParamData("meanWidthKernel")->bind(&meanWidthKernel);
        mImageBinaryModule.getParamData("meanHeightKernel")->bind(&meanHeightKernel);
        mImageBinaryModule.getParamData("compareType")->bind(&compareType);
        mImageBinaryModule.getParamData("thresholdDiff")->bind(&thresholdDiff);
        mImageBinaryModule.getParamData("gaussianKernel")->bind(&gaussianKernel);
        mImageBinaryModule.getParamData("gaussianStandardDeviation")->bind(&gaussianStandardDeviation);
        mImageBinaryModule.getParamData("sauvolaWidthKernel")->bind(&sauvolaWidthKernel);
        mImageBinaryModule.getParamData("sauvolaHeightKernel")->bind(&sauvolaHeightKernel);
        mImageBinaryModule.getParamData("sauvolaValue")->bind(&sauvolaValue);
        mImageBinaryModule.getParamData("sauvolaRange")->bind(&sauvolaRange);
        mImageBinaryModule.getParamData("sauvolaSegmentType")->bind(&sauvolaSegmentType);

        start = std::chrono::high_resolution_clock::now();
        int result = mImageBinaryModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;

        std::string binaryTypeName;
        switch (binaryType)
        {
        case 1:
            binaryTypeName = "均值二值化";
            break;
        case 2:
            binaryTypeName = "高斯二值化";
            break;
        case 3:
            binaryTypeName = "Sauvola二值化";
            break;
        case 4:
            binaryTypeName = "自动二值化";
            break;
        }

        std::cout << binaryTypeName << "模块执行耗时: " << elapsed.count() << " 毫秒, 结果: "
                  << (result == 0 ? "成功" : "失败") << std::endl;

        if (result == 0)
        {
            KImage2Data image2Out;
            image2Out.bind(mImageBinaryModule.getResultData("image2Out"));
            std::string filename = "saveimages/binary_result_type_" + std::to_string(binaryType) + ".jpg";
            cv::imwrite(filename, image2Out.Image2().src());
            std::cout << "保存结果图像: " << filename << std::endl;
        }
    }
    break;
    case ImageProcessType_Morphology:
    {
        // 测试所有形态学操作类型和形状

        int morphType = 2;
        int morphShape = 0;
        KImageMorphologyModule mImageMorphologyModule;
        mImageMorphologyModule.setId("mImageMorphologyModule");

        // 设置参数
        KInt morphologyType, morphologyShape, morphologyIterations;
        KInt morphologyWidthKernel, morphologyHeightKernel;

        morphologyType.setValue(morphType);
        morphologyShape.setValue(morphShape);
        morphologyIterations.setValue(2); // 迭代2次
        morphologyWidthKernel.setValue(5);
        morphologyHeightKernel.setValue(5);

        // 绑定参数
        mImageMorphologyModule.getParamData("image2In")->bind(&image2In);
        mImageMorphologyModule.getParamData("morphologyType")->bind(&morphologyType);
        mImageMorphologyModule.getParamData("morphologyShape")->bind(&morphologyShape);
        mImageMorphologyModule.getParamData("morphologyIterations")->bind(&morphologyIterations);
        mImageMorphologyModule.getParamData("morphologyWidthKernel")->bind(&morphologyWidthKernel);
        mImageMorphologyModule.getParamData("morphologyHeightKernel")->bind(&morphologyHeightKernel);

        start = std::chrono::high_resolution_clock::now();
        int result = mImageMorphologyModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;

        std::string morphTypeName, morphShapeName;
        switch (morphType)
        {
        case 0:
            morphTypeName = "膨胀";
            break;
        case 1:
            morphTypeName = "腐蚀";
            break;
        case 2:
            morphTypeName = "开运算";
            break;
        case 3:
            morphTypeName = "闭运算";
            break;
        }

        switch (morphShape)
        {
        case 0:
            morphShapeName = "矩形";
            break;
        case 1:
            morphShapeName = "椭圆";
            break;
        case 2:
            morphShapeName = "十字形";
            break;
        }

        std::cout << morphTypeName << "(" << morphShapeName << ")模块执行耗时: "
                  << elapsed.count() << " 毫秒, 结果: "
                  << (result == 0 ? "成功" : "失败") << std::endl;

        if (result == 0)
        {
            KImage2Data image2Out;
            image2Out.bind(mImageMorphologyModule.getResultData("image2Out"));
            std::string filename = "saveimages/morphology_type_" + std::to_string(morphType) + "_shape_" + std::to_string(morphShape) + ".jpg";
            cv::imwrite(filename, image2Out.Image2().src());
            std::cout << "保存结果图像: " << filename << std::endl;
        }
    }
    break;
    case ImageProcessType_ShadowCorrection:
    {
        // 测试阴影校正模块的两种滤波方向
        int direction = 2;
        KImageShadowCorrectionModule mImageShadowCorrectionModule;
        mImageShadowCorrectionModule.setId("mImageShadowCorrectionModule");

        // 设置参数
        KInt kernelSize, directionParam, gain, brightnessCompensation, noise;

        kernelSize.setValue(15);              // 滤波核大小
        directionParam.setValue(direction);   // 滤波方向：0-X方向，1-Y方向，2-XY方向
        gain.setValue(60);                    // 增益系数
        brightnessCompensation.setValue(140); // 亮度补偿
        noise.setValue(0);                    // 噪声阈值

        // 绑定参数
        mImageShadowCorrectionModule.getParamData("image2In")->bind(&image2In);
        mImageShadowCorrectionModule.getParamData("kernelSize")->bind(&kernelSize);
        mImageShadowCorrectionModule.getParamData("direction")->bind(&directionParam);
        mImageShadowCorrectionModule.getParamData("gain")->bind(&gain);
        mImageShadowCorrectionModule.getParamData("brightnessCompensation")->bind(&brightnessCompensation);
        mImageShadowCorrectionModule.getParamData("noise")->bind(&noise);

        start = std::chrono::high_resolution_clock::now();
        int result = mImageShadowCorrectionModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;

        std::string directionName = (direction == 0)   ? "X方向滤波"
                                    : (direction == 1) ? "Y方向滤波"
                                                       : "XY方向滤波";

        std::cout << "阴影校正(" << directionName << ")模块执行耗时: "
                  << elapsed.count() << " 毫秒, 结果: "
                  << (result == 0 ? "成功" : "失败") << std::endl;

        if (result == 0)
        {
            KImage2Data image2Out;
            image2Out.bind(mImageShadowCorrectionModule.getResultData("image2Out"));
            std::string filename = "saveimages/shadow_correction_direction_" + std::to_string(direction) + ".jpg";
            cv::imwrite(filename, image2Out.Image2().src());
            std::cout << "保存结果图像: " << filename << std::endl;
        }
    }
    break;
    default:
    {
    }
    break;
    }
    return 0;
}