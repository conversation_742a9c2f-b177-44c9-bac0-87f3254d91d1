#include "KImageProcess.h"

#include "algorithm/KImageMarker.h"
#include "KFactory.h"

#pragma region 图像固定比例缩放模块
KKeepRatioResizeModule::KKeepRatioResizeModule()
    : KModule()
{
    params().add("image2In", &_image2In);
    params().add("widthorheight", &_widthorheight);
    params().add("dstheight", &_dstheight);
    params().add("dstwidth", &_dstwidth);

    _dstheight.setRange(0, 2048);
    _dstwidth.setRange(0, 2448);
    result().add("image2Out", &_image2Out); // 考虑内存在哪里分配
}

REGISTER_MODULE(KKeepRatioResizeModule);

int KKeepRatioResizeModule::run()
{
    if (!enable())
        return KCV_OK;

    int ori_height = _image2In.Image2().height();
    int ori_width = _image2In.Image2().width();

    cv::Mat imgdst = cv::Mat::zeros(_dstheight.iValue(), _dstwidth.iValue(), CV_8UC3);
    if (_widthorheight.iValue() == 0)
    {
        double new_width = std::ceil(static_cast<double>(_dstheight.iValue()) / ori_height * ori_width);
        int resize_width = std::min(_dstwidth.iValue(), static_cast<int>(new_width));

        cv::Mat imgtemp;
        cv::resize(_image2In.Image2().src(), imgtemp, cv::Size(resize_width, _dstheight.iValue()));

        if (resize_width < _dstwidth.iValue())
        {
            imgtemp.copyTo(imgdst(cv::Rect(0, 0, resize_width, _dstheight.iValue())));
        }
        else
        {
            cv::resize(imgtemp, imgdst, cv::Size(_dstwidth.iValue(), _dstheight.iValue()));
        }
    }
    else
    {
        double new_height = std::ceil(static_cast<double>(_dstwidth.iValue()) / ori_width * ori_height);
        int resize_height = std::min(_dstheight.iValue(), static_cast<int>(new_height));

        cv::Mat imgtemp;
        cv::resize(_image2In.Image2().src(), imgtemp, cv::Size(_dstwidth.iValue(), resize_height));

        if (resize_height < _dstheight.iValue())
        {
            imgtemp.copyTo(imgdst(cv::Rect(0, 0, _dstwidth.iValue(), resize_height)));
        }
        else
        {
            cv::resize(imgtemp, imgdst, cv::Size(_dstwidth.iValue(), _dstheight.iValue()));
        }
    }

    _image2Out.KImage2Ptr()->src() = imgdst.clone();

    return KCV_OK;
}

#pragma endregion

#pragma region 图像缩放模块
KImageResizeModule::KImageResizeModule()
    : KModule()
{
    params().add("image2In", &_image2In);
    params().add("interpolationType", &_interpolationType);
    params().add("dstheight", &_dstheight);
    params().add("dstwidth", &_dstwidth);

    result().add("image2Out", &_image2Out);
}

REGISTER_MODULE(KImageResizeModule);

int KImageResizeModule::run()
{
    if (!enable())
        return KCV_OK;

    cv::Mat imgdst;
    cv::resize(_image2In.Image2().src(), imgdst, cv::Size(_dstwidth.iValue(), _dstheight.iValue()), 0, 0, _interpolationType.iValue());

    _image2Out.KImage2Ptr()->src() = imgdst;
    return KCV_OK;
}
#pragma endregion

#pragma region 图像二值化模块
KImageBinaryModule::KImageBinaryModule()
    : KModule()
{
    params().add("image2In", &_image2In);

    params().add("binaryType", &_binaryType);

    params().add("lowThreshold", &_lowThreshold);
    params().add("highThreshold", &_highThreshold);

    _lowThreshold.setRange(0, 255);
    _highThreshold.setRange(0, 255);

    params().add("meanWidthKernel", &_meanWidthKernel);
    params().add("meanHeightKernel", &_meanHeightKernel);
    _meanWidthKernel.setRange(1, 51);
    _meanHeightKernel.setRange(1, 51);

    params().add("thresholdType", &_thresholdType);
    _thresholdType.setRange(0, 1);

    params().add("gaussianKernel", &_gaussianKernel);
    params().add("gaussianStandardDeviation", &_gaussianStandardDeviation);
    _gaussianKernel.setRange(1, 101);
    _gaussianStandardDeviation.setRange(0.1, 100.0);

    params().add("compareType", &_compareType);
    params().add("thresholdDiff", &_thresholdDiff);
    _compareType.setRange(0, 3);
    _thresholdDiff.setRange(-255, 255);

    params().add("sauvolaWidthKernel", &_sauvolaWidthKernel);
    params().add("sauvolaHeightKernel", &_sauvolaHeightKernel);
    params().add("sauvolaValue", &_sauvolaValue);
    params().add("sauvolaRange", &_sauvolaRange);
    params().add("sauvolaSegmentType", &_sauvolaSegmentType);
    _sauvolaWidthKernel.setRange(1, 51);
    _sauvolaHeightKernel.setRange(1, 51);
    _sauvolaValue.setRange(0.0, 1.0);
    _sauvolaRange.setRange(0.1, 255.0);
    _sauvolaSegmentType.setRange(0, 1);

    result().add("image2Out", &_image2Out);
}

int KImageBinaryModule::run()
{
    cv::Mat input = _image2In.Image2().src();
    if (input.empty())
    {
        std::cout << "KImageBinaryModule: 输入图像为空" << std::endl;
        return KCV_NG;
    }
    cv::Mat gray;
    if (input.channels() == 3)
    {
        cv::cvtColor(input, gray, cv::COLOR_BGR2GRAY); // 转灰度
    }
    else
    {
        gray = input.clone();
    }
    cv::Mat output;

    int binaryType = _binaryType.iValue();
    switch (binaryType)
    {
    case 0: // 阈值化
    {
        int thresholdType = (_thresholdType.iValue() == 0) ? cv::THRESH_BINARY : cv::THRESH_BINARY_INV;
        cv::threshold(gray, output, _lowThreshold.iValue(), _highThreshold.iValue(), thresholdType);
    }
    break;
    case 1: // 均值二值化
    {
        // 根据规范实现均值二值化
        int kernelWidth = _meanWidthKernel.iValue();
        int kernelHeight = _meanHeightKernel.iValue();

        // 1. 对原图进行均值滤波得到背景图
        cv::Mat background;
        cv::blur(gray, background, cv::Size(kernelWidth, kernelHeight));
        // cv::imwrite("saveimages/background.jpg", background);
        // cv::imwrite("saveimages/gray.jpg", gray);

        // 2. 计算原图和背景图的差值
        cv::Mat diff;
        cv::subtract(gray, background, diff);
        // cv::imwrite("saveimages/diff.jpg", diff);

        // 3. 根据比较类型和阈值偏移量判断像素点
        output = cv::Mat::zeros(gray.size(), CV_8UC1);
        int compareType = _compareType.iValue();
        int thresholdDiff = _thresholdDiff.iValue();

        for (int y = 0; y < diff.rows; y++)
        {
            for (int x = 0; x < diff.cols; x++)
            {
                int diffValue = diff.at<uchar>(y, x);
                bool isTarget = false;

                switch (compareType)
                {
                case 0: // ≤
                    isTarget = (diffValue <= thresholdDiff);
                    break;
                case 1: // ≥
                    isTarget = (diffValue >= thresholdDiff);
                    break;
                case 2: // =
                    isTarget = (diffValue == thresholdDiff);
                    break;
                case 3: // ≠
                    isTarget = (diffValue != thresholdDiff);
                    break;
                }
                if (isTarget)
                {
                    output.at<uchar>(y, x) = (_thresholdType.iValue() == 0) ? 255 : 0;
                }
                else
                {
                    output.at<uchar>(y, x) = (_thresholdType.iValue() == 0) ? 0 : 255;
                }
            }
        }
    }
    break;
    case 2: // 高斯二值化
    {
        // 根据规范实现高斯二值化
        int kernelSize = _gaussianKernel.iValue();
        double sigma = _gaussianStandardDeviation.dValue();

        // 确保核大小为奇数
        if (kernelSize % 2 == 0)
            kernelSize++;

        // 1. 对原图进行高斯滤波得到背景图
        cv::Mat background;
        cv::GaussianBlur(gray, background, cv::Size(kernelSize, kernelSize), sigma);
        // cv::imwrite("saveimages/background.jpg", background);
        // cv::imwrite("saveimages/gray.jpg", gray);

        // 2. 计算原图和背景图的差值
        cv::Mat diff;
        cv::subtract(gray, background, diff);
        // cv::imwrite("saveimages/diff.jpg", diff);

        // 3. 根据比较类型和阈值偏移量判断像素点
        output = cv::Mat::zeros(gray.size(), CV_8UC1);
        int compareType = _compareType.iValue();
        int thresholdDiff = _thresholdDiff.iValue();

        for (int y = 0; y < diff.rows; y++)
        {
            for (int x = 0; x < diff.cols; x++)
            {
                int diffValue = diff.at<uchar>(y, x);
                bool isTarget = false;

                switch (compareType)
                {
                case 0: // ≤
                    isTarget = (diffValue <= thresholdDiff);
                    break;
                case 1: // ≥
                    isTarget = (diffValue >= thresholdDiff);
                    break;
                case 2: // =
                    isTarget = (diffValue == thresholdDiff);
                    break;
                case 3: // ≠
                    isTarget = (diffValue != thresholdDiff);
                    break;
                }

                if (isTarget)
                {
                    output.at<uchar>(y, x) = (_thresholdType.iValue() == 0) ? 255 : 0;
                }
                else
                {
                    output.at<uchar>(y, x) = (_thresholdType.iValue() == 0) ? 0 : 255;
                }
            }
        }
    }
    break;
    case 3: // Sauvola二值化
    {
        // 根据规范实现Sauvola算法
        int kernelWidth = _sauvolaWidthKernel.iValue();
        int kernelHeight = _sauvolaHeightKernel.iValue();
        double k = _sauvolaValue.dValue();
        double r = _sauvolaRange.dValue();
        int segmentType = _sauvolaSegmentType.iValue();

        // 确保核大小为奇数
        if (kernelWidth % 2 == 0)
            kernelWidth++;
        if (kernelHeight % 2 == 0)
            kernelHeight++;

        cv::Mat mean, stddev;
        cv::Mat gray_f;
        gray.convertTo(gray_f, CV_32F);

        // 计算局部均值和标准差
        cv::boxFilter(gray_f, mean, CV_32F, cv::Size(kernelWidth, kernelHeight));
        cv::Mat sqr;
        cv::multiply(gray_f, gray_f, sqr);
        cv::Mat sqr_mean;
        cv::boxFilter(sqr, sqr_mean, CV_32F, cv::Size(kernelWidth, kernelHeight));
        cv::multiply(mean, mean, stddev);
        cv::subtract(sqr_mean, stddev, stddev);
        cv::sqrt(stddev, stddev);

        // Sauvola阈值计算: m(1 + k(std/r - 1))
        cv::Mat threshold = mean.mul(1.0 + k * (stddev / r - 1.0));

        // 二值化
        output = cv::Mat::zeros(gray.size(), CV_8UC1);

        if (segmentType == 0) // 暗背景: m(1 + k(std/r - 1)) > pixel 则为目标
        {
            cv::Mat mask = (threshold > gray_f);
            output.setTo((_thresholdType.iValue() == 0) ? 255 : 0, mask);
            output.setTo((_thresholdType.iValue() == 0) ? 0 : 255, ~mask);
        }
        else // 亮背景: m(1 + k(std/r - 1)) < pixel 则为目标
        {
            cv::Mat mask = (threshold < gray_f);
            output.setTo((_thresholdType.iValue() == 0) ? 255 : 0, mask);
            output.setTo((_thresholdType.iValue() == 0) ? 0 : 255, ~mask);
        }
    }
    break;
    case 4: // 自动二值化
    {
        // 使用Otsu自动阈值
        int thresholdType = (_thresholdType.iValue() == 0) ? cv::THRESH_BINARY : cv::THRESH_BINARY_INV;
        cv::threshold(gray, output, 0, _highThreshold.iValue(), thresholdType | cv::THRESH_OTSU);
    }
    break;
    default:
        break;
    }
    _image2Out.Image2().src() = output;
    return 0;
}

REGISTER_MODULE(KImageBinaryModule);

#pragma endregion

#pragma region 图像滤波模块
KImageFilterModule::KImageFilterModule()
    : KModule()
{
    params().add("image2In", &_image2In);
    params().add("filterType", &_filterType);
    params().add("gaussianKernel", &_gaussianKernel);
    params().add("medianKernel", &_medianKernel);
    params().add("meanWidthKernel", &_meanWidthKernel);
    params().add("meanHeightKernel", &_meanHeightKernel);

    result().add("image2Out", &_image2Out);
}

REGISTER_MODULE(KImageFilterModule);

int KImageFilterModule::run()
{
    if (!enable())
        return KCV_OK;

    switch (_filterType.iValue())
    {
    case 0: // 高斯滤波
        cv::GaussianBlur(_image2In.Image2().src(), _image2Out.Image2().src(), cv::Size(_gaussianKernel.iValue(), _gaussianKernel.iValue()), 1.0);
        break;
    case 1: // 中值滤波
        cv::medianBlur(_image2In.Image2().src(), _image2Out.Image2().src(), _medianKernel.iValue());
        break;
    case 2: // 均值滤波
        cv::blur(_image2In.Image2().src(), _image2Out.Image2().src(), cv::Size(_meanWidthKernel.iValue(), _meanHeightKernel.iValue()));
        break;
    case 3: // 取反
        cv::bitwise_not(_image2In.Image2().src(), _image2Out.Image2().src());
        std::cout << "bitwise_not" << std::endl;
        break;
    case 4: // 边缘提取
    {
        cv::Mat gray;
        if (_image2In.Image2().src().channels() == 3)
        {
            cv::cvtColor(_image2In.Image2().src(), gray, cv::COLOR_BGR2GRAY); // 转灰度
        }
        else
        {
            gray = _image2In.Image2().src().clone();
        }

        cv::Mat grad_x, grad_y;
        cv::Sobel(gray, grad_x, CV_16S, 1, 0); // X方向梯度
        cv::Sobel(gray, grad_y, CV_16S, 0, 1); // Y方向梯度

        cv::convertScaleAbs(grad_x, grad_x);
        cv::convertScaleAbs(grad_y, grad_y);
        cv::addWeighted(grad_x, 0.5, grad_y, 0.5, 0, _image2Out.Image2().src()); // 合并梯度
    }
    break;
    default:
        _image2Out.Image2().src() = _image2In.Image2().src().clone();
        break;
    }
    return KCV_OK;
}
#pragma endregion

#pragma region 图像增强模块
KImageEnhancedModule::KImageEnhancedModule()
    : KModule()
{
    params().add("imageMarker", &_imageMarker);
    params().add("smartAi", &_smartAi);
    params().add("filterType", &_filterType);
    params().add("enhanceNum", &_enhanceNum);

    _enhanceNum.setValue(1);
    _enhanceNum.setRange(1, 10);

    params().add("image2In", &_image2In);
    params().add("alpha", &_alpha);
    params().add("beta", &_beta);
    params().add("gamma", &_gamma);

    KSmartParamAI *ai = new KSmartParamAI;
    _smartAi.setAIParam(ai);

    _alpha.setRange(0, 10);
    _alpha.setValue(1.0);
    _beta.setRange(-255, 255);
    _beta.setValue(100);
    _gamma.setRange(0, 5);
    _gamma.setValue(0.5);
    result().add("image2Out", &_image2Out);
}

REGISTER_MODULE(KImageEnhancedModule);
cv::Mat KImageEnhancedModule::EnhancedPostProcess(KBitlandAIEngineInferData *dataEnity)
{
    int outsize = dataEnity->OutputDim[0].Channel;
    int outwidth = dataEnity->OutputDim[0].Width, outheight = dataEnity->OutputDim[0].Height;

    cv::Mat outPutImg1(dataEnity->OutputDim[0].Height, dataEnity->OutputDim[0].Width, CV_32FC1, (float *)dataEnity->rltdata);
    cv::Mat outPutImg2(dataEnity->OutputDim[0].Height, dataEnity->OutputDim[0].Width, CV_32FC1, (float *)dataEnity->rltdata + outwidth * outheight);
    cv::Mat outPutImg3(dataEnity->OutputDim[0].Height, dataEnity->OutputDim[0].Width, CV_32FC1, (float *)dataEnity->rltdata + outwidth * outheight * 2);
    // std::vector<cv::Mat> channelsVec = {outPutImg3, outPutImg2, outPutImg1};
    std::vector<cv::Mat> channelsVec = {outPutImg1, outPutImg2, outPutImg3};
    cv::Mat float_image, int_image;
    cv::merge(channelsVec, float_image);
    float_image.convertTo(int_image, CV_8UC3, 255.0);
    return int_image;
}
int KImageEnhancedModule::EnhancedInfer()
{
    KImageMarker *marker = (KImageMarker *)_imageMarker.imageMarker();
    if (!_smartAi.smartAiData()) // ai参数是否设置
    {
        if (marker)
        {
            KObject *obj = (KObject *)this;
            std::string s = "模块 " + obj->name() + " 没有模型 !";
            marker->addText(s.c_str(), KPoint(10, 50), KCV_RED);
            printf("KInferModule add text : %s \n", s.c_str());
        }
        printf(" KInferModule::run() !_smartAi.smartAiData() return, m id = %s\n", id().c_str());
        return KCV_OK;
    }
    KAIEngineInferEnity *pEnity = _smartAi.smartAiData()->enity();
    KBitlandAIEngineInferEnity *benity = dynamic_cast<KBitlandAIEngineInferEnity *>(pEnity);
    if (!benity)
    {
        if (marker)
        {
            KObject *obj = (KObject *)this;
            std::string s = "模块 " + obj->name() + " 没有模型 !";
            marker->addText(s.c_str(), KPoint(10, 50), KCV_RED);
            printf("KInferModule add text : %s \n", s.c_str());
        }
        printf(" KInferModule::run() !benity return, m id = %s\n", id().c_str());

        return KCV_OK;
    }

    benity->infer(_image2In.KImage2Ptr());
    // 取推理结果

    KBitlandAIEngineInferData *dataEnity = (KBitlandAIEngineInferData *)(benity->data());
    _image2Out.Image2().src() = EnhancedPostProcess(dataEnity);
    if (_enhanceNum.iValue() > 1)
    {
        for (int i = 1; i < _enhanceNum.iValue(); i++)
        {
            cv::resize(_image2Out.Image2().src(), _image2Out.Image2().src(), cv::Size(640, 640));
            benity->infer(_image2Out.KImage2Ptr());
            _image2Out.Image2().src() = EnhancedPostProcess(dataEnity);
        }
    }
    return 0;
}

/**
 * @brief Gamma校正调整亮度
 * @param image 输入图像
 * @param gamma Gamma值（<1变亮，>1变暗）
 * @return 调整后的图像
 */
int KImageEnhancedModule::adjustBrightnessGamma(const cv::Mat &image, double gamma)
{
    cv::Mat lookupTable(1, 256, CV_8U);
    uchar *p = lookupTable.ptr();
    for (int i = 0; i < 256; ++i)
    {
        p[i] = cv::saturate_cast<uchar>(pow(i / 255.0, gamma) * 255.0);
    }

    cv::LUT(image, lookupTable, _image2Out.Image2().src());
    return 0;
}

// 方法2：自适应直方图均衡化（CLAHE，适用于彩色/灰度图像）
int KImageEnhancedModule::enhanceContrast_CLAHE(const cv::Mat &input, double clipLimit, cv::Size gridSize)
{

    // 彩色图像处理
    if (input.channels() == 3)
    {
        cv::Mat lab;
        cv::cvtColor(input, lab, cv::COLOR_RGB2Lab); // 转换到Lab色彩空间 注意输入图像顺序

        std::vector<cv::Mat> channels;
        cv::split(lab, channels);

        // 仅对L通道（亮度）进行增强
        cv::Ptr<cv::CLAHE> clahe = cv::createCLAHE(clipLimit, gridSize);
        clahe->apply(channels[0], channels[0]);

        cv::merge(channels, lab);
        cv::cvtColor(lab, _image2Out.Image2().src(), cv::COLOR_Lab2RGB); // 注意输出图像顺序
    }
    // 灰度图像处理
    else
    {
        cv::Ptr<cv::CLAHE> clahe = cv::createCLAHE(clipLimit, gridSize);
        clahe->apply(input, _image2Out.Image2().src());
    }

    return 0;
}

int KImageEnhancedModule::run()
{
    if (!enable())
        return KCV_OK;

    switch (_filterType.iValue())
    {
    case 0:
        EnhancedInfer(); // 图像增强模型 增强图像
        break;
    case 1:
        _image2In.Image2().src().convertTo(_image2Out.Image2().src(), -1, _alpha.dValue(), _beta.iValue()); // alpha=1, beta=亮度值 亮
        break;
    case 2:
        adjustBrightnessGamma(_image2In.Image2().src(), _gamma.dValue()); // gamma校正
        break;
    case 3:
        enhanceContrast_CLAHE(_image2In.Image2().src()); // CLAHE自适应直方图均衡化
        break;
    default:
        _image2Out.Image2().src() = _image2In.Image2().src().clone();
        break;
    }
    return KCV_OK;
}
#pragma endregion

#pragma region 图像形态学处理模块
KImageMorphologyModule::KImageMorphologyModule()
    : KModule()
{
    params().add("image2In", &_image2In);
    params().add("morphologyType", &_morphologyType);
    params().add("morphologyShape", &_morphologyShape);
    params().add("morphologyIterations", &_morphologyIterations);
    params().add("morphologyWidthKernel", &_morphologyWidthKernel);
    params().add("morphologyHeightKernel", &_morphologyHeightKernel);

    result().add("image2Out", &_image2Out);
}

REGISTER_MODULE(KImageMorphologyModule);

int KImageMorphologyModule::run()
{
    cv::Mat input = _image2In.Image2().src();
    if (input.empty())
    {
        std::cout << "KImageBinaryModule: 输入图像为空" << std::endl;
        return KCV_NG;
    }
    cv::Mat gray;
    if (input.channels() == 3)
    {
        cv::cvtColor(input, gray, cv::COLOR_BGR2GRAY); // 转灰度
    }
    else
    {
        gray = input.clone();
    }
    cv::Mat output;
    switch (_morphologyType.iValue())
    {
    case 0: // 膨胀
        cv::dilate(gray, output, cv::getStructuringElement(cv::MORPH_RECT, cv::Size(_morphologyWidthKernel.iValue(), _morphologyHeightKernel.iValue())));
        break;
    case 1: // 腐蚀
        cv::erode(gray, output, cv::getStructuringElement(cv::MORPH_RECT, cv::Size(_morphologyWidthKernel.iValue(), _morphologyHeightKernel.iValue())));
        break;
    case 2: // 开运算
        cv::morphologyEx(gray, output, cv::MORPH_OPEN, cv::getStructuringElement(cv::MORPH_RECT, cv::Size(_morphologyWidthKernel.iValue(), _morphologyHeightKernel.iValue())));
        break;
    case 3: // 闭运算
        cv::morphologyEx(gray, output, cv::MORPH_CLOSE, cv::getStructuringElement(cv::MORPH_RECT, cv::Size(_morphologyWidthKernel.iValue(), _morphologyHeightKernel.iValue())));
        break;
    default:
        output = gray.clone();
        break;
    }
    _image2Out.Image2().src() = output;
    return KCV_OK;
}
#pragma endregion