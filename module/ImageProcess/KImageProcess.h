
#pragma once
#include "KModuleDefine.h"
#include "aiEngine/KBitlandAIEnity.h"

#pragma region 图像固定比例缩放模块
class KKeepRatioResizeModule : public KModule
{
public:
    KKeepRatioResizeModule();
    int run() override;

private:
    KImage2Data _image2In;
    KInt _widthorheight;
    KInt _dstheight;
    KInt _dstwidth;
    KImage2Data _image2Out;
};

#pragma endregion

#pragma region 图像缩放模块
class KImageResizeModule : public KModule
{
public:
    KImageResizeModule();
    int run() override;

private:
    KImage2Data _image2In;
    KInt _interpolationType;
    KInt _dstheight;
    KInt _dstwidth;
    KImage2Data _image2Out;
};
#pragma endregion

#pragma region 图像二值化模块
class KImageBinaryModule : public KModule
{
public:
    KImageBinaryModule();
    int run() override;

private:
    KImage2Data _image2In;
    KInt _binaryType;    // 二值化类型 0:阈值化 1-均值二值化 2-高斯二值化 3-Sauvola二值化 4-自动二值化
    KInt _lowThreshold;  // 低阈值
    KInt _highThreshold; // 高阈值

    KInt _meanWidthKernel;  // 均值核宽度
    KInt _meanHeightKernel; // 均值核高度

    KInt _thresholdType; // 阈值类型 0:二进制 1:反二进制

    KInt _gaussianKernel;               // 高斯核大小
    KDouble _gaussianStandardDeviation; // 高斯标准差

    KInt _compareType;   // 比较类型 0:小于等于 1:大于等于 2:等于 3:不等于
    KInt _thresholdDiff; // 阈值差值

    KInt _sauvolaWidthKernel;  // Sauvola核宽度
    KInt _sauvolaHeightKernel; // Sauvola核高度
    KDouble _sauvolaValue;     // Sauvola校正系数
    KDouble _sauvolaRange;     // Sauvola范围
    KInt _sauvolaSegmentType;  // 分割类型 0-暗于背景 1-RunParam_Brighter Than background

    KImage2Data _image2Out;
};
#pragma endregion

#pragma region 图像滤波模块
class KImageFilterModule : public KModule
{
public:
    KImageFilterModule();
    int run() override;

private:
    KInt _filterType;
    KImage2Data _image2In;
    KImage2Data _image2Out;

    KInt _gaussianKernel;
    KInt _medianKernel;
    KInt _meanWidthKernel;
    KInt _meanHeightKernel;
};
#pragma endregion

#pragma region 图像增强模块
class KImageEnhancedModule : public KModule
{
public:
    KImageEnhancedModule();
    int run() override;

    int EnhancedInfer();
    cv::Mat EnhancedPostProcess(KBitlandAIEngineInferData *dataEnity);

    int adjustBrightnessGamma(const cv::Mat &image, double gamma);
    int enhanceContrast_CLAHE(const cv::Mat &input, double clipLimit = 2.0, cv::Size gridSize = cv::Size(8, 8));

private:
    KImageMarkerData _imageMarker; // 绘制工具
    KSmartParamAIData _smartAi;    // 输入模型

    KInt _filterType;
    KInt _enhanceNum; // 增强次数
    KImage2Data _image2In;
    KImage2Data _image2Out;

    KDouble _alpha;
    KInt _beta;
    KDouble _gamma;
};
#pragma endregion

#pragma region 图像形态学处理模块
class KImageMorphologyModule : public KModule
{
public:
    KImageMorphologyModule();
    int run() override;

private:
    KImage2Data _image2In;
    KImage2Data _image2Out;

    KInt _morphologyType;        // 形态学类型 0-膨胀 1-腐蚀 2-开运算 3-闭运算
    KInt _morphologyShape;       // 形态学形状 0-矩形 1-椭圆 2-十字形
    KInt _morphologyIterations;  // 形态学迭代次数
    KInt _morphologyWidthKernel; // 形态学核宽度
    KInt _morphologyHeightKernel; // 形态学核高度
};
#pragma endregion