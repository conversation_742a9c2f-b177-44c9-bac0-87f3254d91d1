# KImageProcessModule 图像处理模块

该模块主要包含基础图像处理功能，包括图像尺寸调整、滤波、增强等预处理操作。

## 模块概述

### 图像固定比例缩放模块 (KKeepRatioResizeModule)
- 在保持图像原始宽高比的情况下调整图像大小
- 可以指定以宽度或高度为基准进行等比例缩放（通过widthorheight参数控制）
- 支持设置目标宽度(dstwidth)和高度(dstheight)
- 自动处理尺寸不匹配情况，保证图像质量
#### 参数配置
- 输入参数:
  - "image2In": 输入图像
  - "widthorheight": 0/1 ，0: 基于高度缩放，1: 基于宽度缩放
  - "dstheight": 目标高度
  - "dstwidth": 目标宽度
- 输出参数:
  - "image2Out": 输出图像

### 图像缩放模块 (KImageResizeModule)
- 直接调整图像到指定尺寸
- 支持多种插值方式（通过interpolationType参数控制）
- 可自定义目标宽度和高度
#### 参数配置
- 输入参数:
  - "image2In": 输入图像
  - "interpolationType": 插值类型，0: 双线性插值，1: 双三次插值，2: 最近邻插值
  - "dstheight": 目标高度
  - "dstwidth": 目标宽度
- 输出参数:
  - "image2Out": 输出图像

### 图像二值化模块 (KImageBinaryModule)
- 支持多种二值化方式（通过binaryType参数控制）：
  - 二进制(0)：使用阈值将图像转换为二值图
  - 反二进制(1)：使用阈值将图像转换为反二值图
  - 均值二值化(2)：使用均值滤波和阈值将图像转换为二值图
  - 高斯二值化(3)：使用高斯滤波和阈值将图像转换为二值图
  - 自动二值化(4)：使用自动阈值将图像转换为二值图
#### 参数配置
- 输入参数:
  - "image2In": 输入图像
  - "binaryType": 二值化类型，0: 二进制，1: 反二进制，2: 均值二值化，3: 高斯二值化，4: 自动二值化
  - "thresholdType": 阈值类型，0: 二进制，1: 反二进制
  - "lowThreshold": 低阈值
  - "highThreshold": 高阈值
  - "meanWidthKernel": 均值核宽度
  - "meanHeightKernel": 均值核高度
  - "gaussianKernel": 高斯核大小
  - "gaussianStandardDeviation": 高斯标准差
  - "sauvolaWidthKernel": Sauvola核宽度
  - "sauvolaHeightKernel": Sauvola核高度
  - "sauvolaValue": SauvolaK值
  - "sauvolaRange": SauvolaR值
  - "sauvolaSegmentType": Sauvola分割类型
  - "compareType": 比较类型，0: 小于等于，1: 大于等于，2: 小于，3: 大于
  - "thresholdDiff": 阈值差值
- 输出参数:
  - "image2Out": 输出图像

### 图像滤波模块 (KImageFilterModule)
- 支持多种滤波方式（通过filterType参数控制）：
  - 高斯滤波(0)：平滑图像，去除噪声，可设置核大小(gaussianKernel)
  - 中值滤波(1)：去除椒盐噪声，可设置核大小(medianKernel)
  - 均值滤波(2)：简单的平滑处理，可设置核宽度(meanWidthKernel)和高度(meanHeightKernel)
  - 图像取反(3)：对图像进行像素值反转
  - 边缘提取(4)：使用Sobel算子提取图像边缘（X、Y方向梯度组合）
#### 参数配置
- 输入参数:
  - "image2In": 输入图像
  - "filterType": 滤波类型，0: 高斯滤波，1: 中值滤波，2: 均值滤波，3: 取反，4: 边缘提取
  - "gaussianKernel": 高斯核大小
  - "medianKernel": 中值核大小
  - "meanWidthKernel": 均值核宽度
  - "meanHeightKernel": 均值核高度
- 输出参数:
  - "image2Out": 输出图像

### 图像增强模块 (KImageEnhancedModule)
- 支持多种增强方式（通过filterType参数控制）：
  - AI模型增强(0)：使用深度学习模型增强图像（需要配置smartAi参数）
  - 线性亮度对比度调整(1)：使用alpha(对比度)和beta(亮度)参数进行线性变换，公式：新像素 = alpha * 原像素 + beta
  - Gamma校正(2)：非线性亮度调整，gamma<1变亮，>1变暗
  - CLAHE自适应直方图均衡化(3)：增强图像对比度，适用于光照不均的场景
#### 参数配置
- 输入参数:
  - "image2In": 输入图像
  - "imageMarker": 图像标记器
  - "smartAi": 智能AI模型配置
  - "filterType": 增强类型，0: AI模型增强，1: 线性亮度对比度调整，2: Gamma校正，3: CLAHE自适应直方图均衡化
  - "enhanceNum": 增强次数，范围[1,10]，默认值1
  - "alpha": 对比度参数，范围[0,10]，默认值0.0
  - "beta": 亮度参数，范围[-255,255]，默认值100
  - "gamma": Gamma校正参数，范围[0,5]，默认值0.5
- 输出参数:
  - "image2Out": 输出图像

### 图像形态学处理模块 (KImageMorphologyModule)
- 支持多种形态学处理方式（通过morphologyType参数控制）：
  - 膨胀(0)：对图像进行膨胀操作，可设置核大小(morphologyWidthKernel)和高度(morphologyHeightKernel)
  - 腐蚀(1)：对图像进行腐蚀操作，可设置核大小(morphologyWidthKernel)和高度(morphologyHeightKernel)
  - 开运算(2)：对图像进行开运算操作，可设置核大小(morphologyWidthKernel)和高度(morphologyHeightKernel)
  - 闭运算(3)：对图像进行闭运算操作，可设置核大小(morphologyWidthKernel)和高度(morphologyHeightKernel)
#### 参数配置
- 输入参数:
  - "image2In": 输入图像
  - "morphologyType": 形态学类型，0: 膨胀，1: 腐蚀，2: 开运算，3: 闭运算
  - "morphologyShape": 形态学形状，0: 矩形，1: 椭圆，2: 十字形
  - "morphologyIterations": 形态学迭代次数
  - "morphologyWidthKernel": 形态学核宽度
  - "morphologyHeightKernel": 形态学核高度
- 输出参数:
  - "image2Out": 输出图像
  
## 使用说明

所有模块均继承自KModule基类，通过REGISTER_MODULE宏进行注册。各模块遵循统一的接口规范：

1. 通过params()方法注册输入参数
2. 通过result()方法注册输出结果
3. 实现run()方法作为主要执行入口
4. 通过enable()判断模块是否启用
5. 返回值KCV_OK表示成功，其他值表示不同类型的错误

## 技术特性

- 基于OpenCV实现核心图像处理功能
- 模块化设计，支持灵活组合和扩展
- 参数化配置，便于调整和优化
- 支持多种图像格式和颜色空间
- 集成AI模型增强能力

## 应用场景

- 图像预处理
- 图像增强与滤波
- 为深度学习和机器视觉算法提供输入数据准备
- 工业视觉检测的基础处理
- 低光照场景图像增强
- 噪声和模糊图像的清晰化处理
