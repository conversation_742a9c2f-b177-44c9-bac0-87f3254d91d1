# KOperatorLibrary
该项目为科亿科技算子开发库，包含算子开发所需的所有依赖项。

## 开发中功能
- 深度学习算子 (Standard)
  - 分类
  - 检测
  - 分割
  - 异常检测
  - 缺陷检测
  - 后处理
- 定位算子 (Position)
  - 位置修正(支持单目标/多目标的坐标缩放与偏移，适配不同分辨率和坐标系)
  - Blob分析(支持二值图像的连通域提取、面积/宽高过滤、最大目标定位和多目标输出)
  - 模板匹配(支持模板匹配，支持旋转匹配)
  - 直线查找(检测并拟合直线)
  - 圆查找(检测并拟合圆)
  - 椭圆查找(检测并拟合椭圆)
  - 字符定位(基于深度学习模型检测字符区域)
  - Blob标签分析(未实现)
  - 图形定位(未实现)
  - 阵列圆查找(未实现)
  - 直线查找组合(未实现)
  - 多直线查找(未实现)
  - 边缘交点(未实现)
  - 四边形查找(未实现)
  - 平行线查找(未实现)
  - 平行线计算(未实现)
  - 矩形检测(未实现)
  - 中线查找(未实现)
  - 垂线查找(未实现)
  - 角平分线查找(未实现)
  - 卡尺工具(未实现)
  - 边缘查找(未实现)
  - 顶点检测(未实现)
  - 路径提取(未实现)
  - 目标跟踪(未实现)
- 图像处理算子 (ImageProcess)
  - 图像尺寸调整(保持宽高比)
  - 图像尺寸调整(普通)
  - 图像滤波(高斯滤波，中值滤波，均值滤波，图像取反，边缘提取[sobel算子])
  - 图像增强(AI模型增强，线性亮度对比度调整，Gamma校正，CLAHE自适应直方图均衡化)
  - 图形二值化(阈值化，均值二值化，高斯二值化，Sauvola二值化，自动二值化)
  - 形态学处理(膨胀，腐蚀，开运算，闭运算)
  - 阴影校正(未实现)
  - 图像组合(未实现)
  - 图像归一化(未实现)
  - 图像运算(未实现)
  - 图像修正(未实现)
  - 仿射变换(未实现)
  - 逆仿射变换(未实现)
  - 几何变换(未实现)
  - 圆环展开(未实现)
  - 拷贝填充(未实现)
  - 畸变矫正(未实现)
  - 清晰度评估(未实现)
  - 图像矫正(未实现)
  - 图像拼接(未实现)
  - 帧平均(未实现)
  - 多图融合(未实现)
- 颜色处理算子 (ColorProcess)
  - 颜色空间转换(RGB到灰度图，RGB到HSV，RGB到HUV)
  - 颜色测量(最大值和最小值，均值和标准差、生成直方图)
  - 颜色提取(根据颜色范围提取图像中特定颜色区域，输出提取区域的面积和二值掩码图像)
  - 颜色识别(未实现)
- 传统缺陷检测算子 (Defect)
  - 表面缺陷检测(基于多行一维高斯条带核，通过旋转生成多个方向的滤波器组，检测无纹理表面上的缺陷)
  - 字符缺陷检测(未实现)
  - 异常检测(未实现)
  - 边缘模型缺陷检测(未实现)
  - 直线边缘缺陷检测(未实现)
  - 直线对缺陷检测(未实现)
  - 圆弧对缺陷检测(未实现)
  - 圆弧边缘缺陷检测(未实现)
  - 边缘组合缺陷检测(未实现)
  - 边缘对组合缺陷检测(未实现)
  - 边缘位置趋势分析(未实现)
  - 边缘对位置趋势分析(未实现)
- 测量算子 (Measure)
  - 点线距离测量(计算点到直线的垂直距离)
  - 点点距离测量(计算两点之间的欧氏距离)
  - 线线测量(计算两条直线的交点坐标，夹角，平行距离)
  - 亮度测量(计算指定ROI区域的最小值、最大值、平均值、标准差和对比度，生成直方图)
  - 直线边缘测量检测(边缘点检测、直线拟合、缺陷识别、缺陷量化)
  - 圆边缘测量检测(轮廓提取、圆形拟合、边缘点搜索、缺陷识别、缺陷量化)
  - 线圆距离测量(计算直线到圆的距离)
  - 圆圆距离测量(计算两个圆的距离)
  - 点圆距离测量(计算点到圆的距离)
  - 像素统计(统计指定ROI区域的像素个数、像素比例、像素均值、像素标准差、像素最大值、像素最小值)
  - 灰度直方图生成(生成灰度直方图，支持可视化显示直方图，支持统计最小灰度值、最大灰度值、中位数灰度值、峰值灰度值、平均灰度值、标准差、对比度)
  - 双特征间距检测(未实现)
- 识别算子 (Recogize)
  - 条码识别(支持二维码和一维条形码的识别)
  - 字符识别(基于OCR实现字符识别)

## 未开发功能
- 图形生成算子 (GraphGeneration)
- 标定算子 (Calibration)
- 计算算子 (Calcuate)
- 拆分组合算子 (Split_Combine)

## 项目结构

```
KOperatorLibrary/
├── module/               # 模块实现目录
│   ├── Standard/         # 深度学习算子实现目录    
│   ├── Position/         # 定位算子实现目录
│   ├── ImageProcess/     # 图像处理算子实现目录
│   ├── ColorProcess/     # 颜色处理算子实现目录
│   ├── Measure/          # 测量算子实现目录
│   ├── Defect/           # 传统缺陷检测算子实现目录
│   ├── Recogize/         # 识别算子实现目录
│   ├── GraphGeneration/  # 图形生成算子实现目录
│   ├── Calibration/      # 标定算子实现目录
│   ├── Calcuate/         # 计算算子实现目录
│   ├── Split_Combine/    # 拆分组合算子实现目录
│   └── ...               # 其他算子实现目录
│
├── test/                 # 测试目录
│   ├── ColorProcess/     # 颜色处理算子测试目录
│   ├── Defect/           # 传统缺陷检测算子测试目录
│   ├── ImageProcess/     # 图像处理算子测试目录
│   ├── Measure/          # 测量算子测试目录
│   ├── Position/         # 定位算子测试目录
│   ├── Recogize/         # 识别算子测试目录
│   └── ...               # 其他算子测试目录
│
├── deps/                 # 依赖文件目录
│   ├── framework/        # 框架实现文件
│   ├── aiEngine/         # AI引擎相关文件
│   ├── algorithm/        # 算法相关文件
│   ├── common/           # 通用工具文件
│   ├── cvText/           # 文本处理相关文件
│   ├── libSimd/          # SIMD加速库（无需编译）
│   └── Simd/             # SIMD加速库（编译用）
│
├── build/                # 构建目录（自动创建）
├── bin/                  # 编译输出目录
│   └── Release/          # 发布版本输出
├── CMakeLists.txt        # 主CMake配置文件
├── build.sh              # 构建脚本
└── README.md             # 项目说明文件
```

## 依赖项

- OpenCV：用于图像处理
- Qt5：用于网络功能（Core、Network、Widgets、Sql、Concurrent）
- 工具链：aarch64 1684交叉编译环境/aarch64 1688交叉编译环境

## 注意项
- 该项目用到了最新创建的1684cmake模块/1688cmake模块，请确保该模块已安装
- 如果未安装，请先安装，将cmake文件夹里对应的cmake文件放置在/usr/local/1684Env/cmake/或/usr/local/1688Env/cmake/里
- 切换编译工具链：修改顶层CMakeLists.txt里的CMAKE_TOOLCHAIN_FILE变量，指向对应cmake文件路径

## 如何编译

1. 确保已安装必要的依赖项和工具链：
   ```
   /usr/local/1684Env/
   /usr/local/1688Env/
   ```

2. 运行构建脚本：
   ```
   chmod +x build.sh
   ./build.sh
   ```
   
   构建脚本支持以下选项：
   - `./build.sh` - 执行构建
   - `./build.sh clean` - 清理构建文件和输出文件
   - `./build.sh rebuild` - 清理并重新构建
   - `./build.sh help` - 显示帮助信息

3. 编译结果将在bin/Release目录下生成。

## 模块依赖关系

CMakeLists.txt中定义了以下依赖关系：
- KAlgorithm 依赖 freetype Simd opencv
- KBitlandAIEngine 依赖 KAlgorithm opencv openssl
- KFramework 依赖 KBitlandAIEngine KAlgorithm
- KStandardModule 依赖 KFramework opencv
- KImageProcessModule 依赖 KFramework opencv
- KColorProcessModule 依赖 KFramework opencv
- KPositionModule 依赖 KFramework opencv

## 注意事项

- 此模块使用C++17标准
- 编译时使用了aarch64 1684交叉编译工具链/aarch64 1688交叉编译工具链
- 编译输出为共享库(.so)文件 